import React, { useState, useContext, useEffect } from "react";
import { Formik, Form, Field } from "formik";
import { Box, TextField, Typography, Button } from "@mui/material";
import { Config, fetchFromStorage } from "src/helpers/context";
import axiosInstance from "src/helpers/Axios/axiosInstance";
import * as Yup from "yup";
import Select, { components } from "react-select";
import _ from "lodash";
import { IntlContext } from "src/App";
import { useSelector } from "react-redux";
import Loader from "src/components/Loader";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router";
import { fetchSubscriptionData } from "src/helpers/store/Actions/SubscriptionData";
import { setApiMessage } from "src/helpers/commonFunctions";

const BillingInfromation = ({ passData, handleUserProfile }) => {
  let dispatch = useDispatch();
  const navigate = useNavigate();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const { ValueContainer, Placeholder } = components;

  const reduxGetUserData = useSelector(
    (state) => state?.reduxData?.SubscripitionData,
  );
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [getUserData, setGetUserData] = useState({});
  const [isDataFetch, setIsDataFetch] = useState(false);
  const [country, setCountry] = useState([]);
  const [state, setState] = useState([]);
  const [countryId, setCountryId] = useState("");

  const [isCountrySelectOpen, setisCountrySelectOpen] = useState(false);
  const [isStateSelectOpen, setisStateSelectOpen] = useState(false);
  const [formvalues, setformvalues] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [billingData, setBillingData] = useState(null);

  console.log("formvalues", formvalues, passData);

  useEffect(() => {
    if (reduxGetUserData && reduxGetUserData !== undefined) {
      setGetUserData(reduxGetUserData);
    }
  }, [reduxGetUserData]);

  useEffect(() => {
    fetchCountry(0);
    setIsDataFetch(true);
    window.addEventListener("resize", function () {
      setScreenWidth(window.innerWidth);
    });
  }, []);

  // const handleRestrictedUser = () => {
  //   const localAuth = fetchFromStorage("auth_token");
  //   let isLogin = localAuth ? true : false;
  //   return isLogin;
  // };

  // const isLogin = handleRestrictedUser();

  const fetchSubscriptionPaymentData = async () => {
    try {
      const { status, data } = await axiosInstance.get(`/user/get-user`);
      if (status === 200) {
        if (Config.release == "IN") {
          data["oddfixtures"] = false;
        }
        dispatch(fetchSubscriptionData(data));
      } else {
        dispatch(fetchSubscriptionData(undefined));
      }
    } catch (err) {
      dispatch(fetchSubscriptionData(undefined));
    }
  };

  // Save billing data to API
  const saveBillingData = async (values) => {
    setIsSaving(true);
    try {
      const payload = {
        addressLine1: values?.billingStreetAddress,
        apt_suite: values?.billingAptSuite,
        postCode: values?.billingPostcode,
        city: values?.billingCitySuburb,
        country: values?.billingCountry,
        state: values?.billingState,
        type: "billing",
        isFeatured: true,
      };

      const { status, data } = await axiosInstance.post(
        `/user/add-billing-address`,
        payload,
      );
      if (status === 200 || status === 201) {
        console.log("objectdata", data);
        navigate("/profile");
        fetchSubscriptionPaymentData();
        handleUserProfile();
        setApiMessage("success", data?.message);
      } else {
        setApiMessage("error", data?.message);
      }
    } catch (err) {
      setApiMessage("error", err?.response?.data?.message);
    } finally {
      setIsSaving(false);
    }
  };

  // country select box api
  const fetchCountry = async () => {
    try {
      const { status, data } = await axiosInstance.get(`public/country`);
      if (status === 200) {
        let response = data?.result?.rows;

        const priorityIds = [13, 101, 230, 231];

        const selectCountryData = response?.filter((item) =>
          priorityIds.includes(item?.id),
        );

        // Keep selectCountryData in the exact order of priorityIds
        const orderedSelectCountryData = priorityIds
          ?.map((id) => selectCountryData.find((item) => item.id === id))
          ?.filter(Boolean); // remove any undefined if ID not found

        const otherCountryData = response
          ?.filter((item) => !priorityIds.includes(item?.id))
          ?.sort((a, b) => a.country.localeCompare(b.country));

        const finalResponse = [
          ...orderedSelectCountryData,
          ...otherCountryData,
        ];

        const newdata = finalResponse.map((item) => ({
          label: item?.country,
          value: item?.id,
        }));

        setCountry(newdata);
      }
    } catch (err) {
      console.error("Error fetching countries", err);
    }
  };

  const fetchState = async (id, page, type, statecount) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/state/country/${id}`,
      );
      if (status === 200) {
        let response = data?.result?.rows;

        let newdata = [];
        let FinalData = response?.map((item) => {
          newdata.push({
            label: item?.state,
            value: item?.id,
          });
        });
        const finalStateData = newdata.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        });

        // If it's a new country selection (type === true), replace the state array completely
        if (type === true) {
          setState(finalStateData);
        } else {
          // For pagination, append to existing states
          let filterData = _.unionBy(state, finalStateData);
          setState(
            _.uniqBy(filterData, function (e) {
              return e.value;
            }),
          );
        }
      }
    } catch (err) {}
  };

  const CustomValueContainer = React.useMemo(() => {
    return ({ children, ...props }) => {
      return (
        <ValueContainer {...props}>
          <Placeholder {...props} isFocused={props.isFocused}>
            {props.selectProps.placeholder}
          </Placeholder>
          {React.Children.map(children, (child) => {
            if (child && child.type !== Placeholder) {
              return child;
            }
            return null;
          })}
        </ValueContainer>
      );
    };
  }, []);

  return (
    <Box>
      {screenWidth > 799 && (
        <Box className="useredit-details">
          <h4>Billing Information</h4>
        </Box>
      )}
      {!isDataFetch ? (
        <div className="allsport-loader-center ">
          <Loader />
        </div>
      ) : (
        <Formik
          enableReinitialize
          initialValues={{
            billingStreetAddress: billingData?.streetAddress || "",
            billingAptSuite: billingData?.aptSuite || "",
            billingPostcode: billingData?.postcode || "",
            billingCitySuburb: billingData?.citySuburb || "",
            billingCountry: billingData?.country || "",
            billingState: billingData?.state || "",
          }}
          validationSchema={Yup.object({
            billingStreetAddress: Yup.string()
              .nullable()
              .required(
                localesData?.validation?.required_message ||
                  "This field is required",
              ),
            billingAptSuite: Yup.string().nullable(),
            billingPostcode: Yup.string()
              .nullable()
              .required(
                localesData?.validation?.required_message ||
                  "This field is required",
              ),
            billingCitySuburb: Yup.string()
              .nullable()
              .required(
                localesData?.validation?.required_message ||
                  "This field is required",
              ),
            billingCountry: Yup.string()
              .nullable()
              .required(
                localesData?.validation?.required_message ||
                  "This field is required",
              ),
            billingState: Yup.string()
              .nullable()
              .required(
                localesData?.validation?.required_message ||
                  "This field is required",
              ),
          })}
          onSubmit={(values, { setSubmitting }) => {
            saveBillingData(values);
            setSubmitting(false);
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            touched,
            values,
            setFieldValue,
          }) => (
            <form onSubmit={handleSubmit}>
              <Box className="">
                <Box className="details">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingAptSuite"
                    label="Apt/Suite (Optional)"
                    error={Boolean(
                      touched?.billingAptSuite && errors?.billingAptSuite,
                    )}
                    helperText={
                      touched?.billingAptSuite ? errors?.billingAptSuite : ""
                    }
                    onChange={handleChange}
                    value={values?.billingAptSuite}
                    onBlur={handleBlur}
                  />
                </Box>
                <Box className="details">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingStreetAddress"
                    label="Street address"
                    error={Boolean(
                      touched?.billingStreetAddress &&
                        errors?.billingStreetAddress,
                    )}
                    helperText={
                      touched?.billingStreetAddress
                        ? errors?.billingStreetAddress
                        : ""
                    }
                    onChange={handleChange}
                    value={values?.billingStreetAddress}
                    onBlur={handleBlur}
                  />
                </Box>

                <Box className="details two-element-grid">
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="number"
                    name="billingPostcode"
                    label="Postcode"
                    error={Boolean(
                      touched?.billingPostcode && errors?.billingPostcode,
                    )}
                    helperText={
                      touched?.billingPostcode ? errors?.billingPostcode : ""
                    }
                    onChange={handleChange}
                    value={values?.billingPostcode}
                    onBlur={handleBlur}
                  />
                  <TextField
                    id="outlined-basic"
                    variant="outlined"
                    className="details-textfield"
                    type="text"
                    name="billingCitySuburb"
                    label="City/Suburb"
                    error={Boolean(
                      touched?.billingCitySuburb && errors?.billingCitySuburb,
                    )}
                    helperText={
                      touched?.billingCitySuburb
                        ? errors?.billingCitySuburb
                        : ""
                    }
                    onChange={handleChange}
                    value={values?.billingCitySuburb}
                    onBlur={handleBlur}
                  />
                </Box>
                <Box className="details two-element-grid">
                  <Box>
                    <Select
                      className="select select-country-state"
                      classNamePrefix="select"
                      isSearchable={true}
                      components={{
                        ValueContainer: CustomValueContainer,
                      }}
                      placeholder="Country"
                      styles={{
                        container: (provided, state) => ({
                          ...provided,
                        }),
                        valueContainer: (provided, state) => ({
                          ...provided,
                          overflow: "visible",
                        }),
                        placeholder: (provided, state) => ({
                          ...provided,
                          position: "absolute",
                          top:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? -11
                              : "auto",
                          backgroundColor:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "white"
                              : "transparent",
                          transition: "top 2s, font-size 0.1s !important",
                          fontSize:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "12px !important",
                          color:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "#4455c7"
                              : "#a4a4a4",
                          padding:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "0px 3px",
                          paddingLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "1px !important",
                          marginLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "7px !important",
                          lineHeight:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "8px !important",
                        }),
                      }}
                      value={
                        !values?.billingCountry
                          ? null
                          : country?.find((item) => {
                              const countryValue = values?.billingCountry;
                              return item?.value == countryValue;
                            })
                      }
                      onChange={(e) => {
                        setFieldValue("billingCountry", e?.value);
                        setState([]);
                        fetchState(e?.value, 0, true, 0);
                        setCountryId(e?.value);
                        setFieldValue("billingState", "");
                        setisCountrySelectOpen(false);
                        setformvalues((prevFormValues) => ({
                          ...prevFormValues,
                          billingCountry: e?.value,
                          billingState: "",
                        }));
                      }}
                      isFocused={isCountrySelectOpen}
                      onFocus={() => setisCountrySelectOpen(true)}
                      onBlur={() => setisCountrySelectOpen(false)}
                      options={country}
                    />

                    <span className="text-danger">
                      {touched?.billingCountry && errors?.billingCountry
                        ? errors?.billingCountry
                        : ""}
                    </span>
                  </Box>
                  <Box>
                    <Select
                      className="select select-country-state"
                      classNamePrefix="select"
                      isSearchable={true}
                      isDisabled={!values?.billingCountry}
                      components={{
                        ValueContainer: CustomValueContainer,
                      }}
                      placeholder="State"
                      styles={{
                        container: (provided, state) => ({
                          ...provided,
                        }),
                        valueContainer: (provided, state) => ({
                          ...provided,
                          overflow: "visible",
                        }),
                        placeholder: (provided, state) => ({
                          ...provided,
                          position: "absolute",
                          top:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? -11
                              : "auto",
                          backgroundColor:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "white"
                              : "transparent",
                          transition: "top 2s, font-size 0.1s !important",
                          fontSize:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "12px !important",
                          color:
                            state.hasValue ||
                            state.selectProps.inputValue ||
                            state.selectProps.menuIsOpen
                              ? "#4455c7"
                              : "#a4a4a4",
                          padding:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "0px 3px",
                          paddingLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "1px !important",
                          marginLeft:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "7px !important",
                          lineHeight:
                            (state.hasValue ||
                              state.selectProps.inputValue ||
                              state.selectProps.menuIsOpen) &&
                            "8px !important",
                        }),
                      }}
                      value={
                        !values?.billingCountry
                          ? null
                          : values?.billingState !== "" &&
                            state?.find((item) => {
                              const stateValue = values?.billingState;
                              return item?.value == stateValue;
                            })
                      }
                      onChange={(e) => {
                        setFieldValue("billingState", e?.value);
                        setisStateSelectOpen(false);
                        setformvalues((prevFormValues) => ({
                          ...prevFormValues,
                          billingState: e?.value,
                        }));
                      }}
                      isFocused={isStateSelectOpen}
                      onFocus={() => setisStateSelectOpen(true)}
                      onBlur={() => setisStateSelectOpen(false)}
                      options={!values?.billingCountry ? [] : state}
                    />

                    <span className="text-danger">
                      {touched?.billingState && errors?.billingState
                        ? errors?.billingState
                        : ""}
                    </span>
                  </Box>
                </Box>

                <Box className="profile-button">
                  <Box className="profile-btn">
                    <Button
                      variant="contained"
                      className="btn-save"
                      type="submit"
                      disabled={isSaving}
                    >
                      Save Billing Information
                    </Button>
                  </Box>
                </Box>
              </Box>
              {setformvalues(values)}
            </form>
          )}
        </Formik>
      )}
    </Box>
  );
};

export default BillingInfromation;
