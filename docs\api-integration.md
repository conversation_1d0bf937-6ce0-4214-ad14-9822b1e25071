# 🔌 API Integration Documentation

This document covers the comprehensive API integration architecture for the SmartB WebApp, including backend services, third-party integrations, and data flow management.

## 📋 Overview

The SmartB platform integrates with multiple APIs to provide:

- Sports and racing data
- User authentication and management
- Payment processing
- Real-time odds and scores
- News and content management
- Fantasy sports functionality
- Social media integration

## 🏗️ API Architecture

### Core API Structure

```
SmartB API Ecosystem
├── Core Backend API
│   ├── User Management
│   ├── Content Management
│   ├── Competition Management
│   └── Analytics
├── Sports Data APIs
│   ├── Live Scores
│   ├── Team/Player Stats
│   ├── Fixtures & Results
│   └── Historical Data
├── Racing Data APIs
│   ├── Race Information
│   ├── Form Guides
│   ├── Results
│   └── Track Data
├── Odds Data APIs
│   ├── Bookmaker Odds
│   ├── Live Updates
│   └── Historical Odds
└── Third-party Services
    ├── Payment Gateways
    ├── Social Media
    ├── Email Services
    └── Analytics
```

### Base Configuration

```javascript
// API Configuration
export const Config = {
  baseURL: process.env.REACT_APP_API_BASE_URL,
  mediaURL: process.env.REACT_APP_MEDIA_URL,
  fantasySiteBaseURL: process.env.REACT_APP_FANTASY_API_BASE_URL,
  fantasyAPI: process.env.REACT_APP_FANTASY_API_URL,
  socketUrl: process.env.REACT_APP_API_SOCKET_BASE_URL,
  socketPath: process.env.REACT_APP_SOCKET_PATH,
  stripeAPIKEY: process.env.REACT_APP_STRIPE_APIKEY_PK,
  GoogleAppID: process.env.REACT_APP_GOOGLE_ID,
  FacebookAppID: process.env.REACT_APP_FACEBOOK_ID,
};
```

## 🎯 Core API Endpoints

### 1. Authentication APIs

#### User Authentication

```javascript
const authEndpoints = {
  // User registration and login
  register: "POST /user/register",
  login: "POST /user/login",
  logout: "POST /user/logout",
  verifyEmail: "POST /user/verify-email",

  // Password management
  forgotPassword: "POST /user/forgot-password",
  resetPassword: "POST /user/reset-password",
  changePassword: "PUT /user/change-password",

  // Profile management
  getProfile: "GET /user/profile",
  updateProfile: "PUT /user/profile",
  deleteAccount: "DELETE /user/account",

  // Social authentication
  googleAuth: "POST /auth/google",
  facebookAuth: "POST /auth/facebook",
};
```

#### Example Implementation

```javascript
// Axios instance with interceptors
const axiosInstance = axios.create({
  baseURL: Config.baseURL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor for authentication
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${JSON.parse(token)}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem("auth_token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);
```

### 2. Sports Data APIs

#### Sports Events and Fixtures

```javascript
const sportsEndpoints = {
  // Fixtures and results
  getFixtures: "GET /sports/:sport/fixtures",
  getResults: "GET /sports/:sport/results",
  getLiveScores: "GET /sports/:sport/live",

  // Team and player data
  getTeams: "GET /sports/:sport/teams",
  getTeamDetails: "GET /sports/teams/:teamId",
  getPlayerStats: "GET /sports/players/:playerId/stats",

  // Competitions and tournaments
  getCompetitions: "GET /sports/:sport/competitions",
  getStandings: "GET /sports/:sport/standings",
  getTournaments: "GET /sports/:sport/tournaments",

  // Statistics and analysis
  getTeamStats: "GET /sports/teams/:teamId/stats",
  getHeadToHead: "GET /sports/teams/:team1Id/vs/:team2Id",
  getFormGuide: "GET /sports/teams/:teamId/form",
};
```

#### Data Models

```javascript
// Sports fixture model
const fixtureModel = {
  id: String,
  homeTeam: {
    id: String,
    name: String,
    logo: String,
  },
  awayTeam: {
    id: String,
    name: String,
    logo: String,
  },
  competition: String,
  venue: String,
  startTime: Date,
  status: String,
  score: Object,
  statistics: Object,
};
```

### 3. Racing Data APIs

#### Racing Information

```javascript
const racingEndpoints = {
  // Race meetings and cards
  getMeetings: "GET /racing/meetings",
  getRaceCard: "GET /racing/meetings/:meetingId/races/:raceId",
  getResults: "GET /racing/results",

  // Form guides and statistics
  getFormGuide: "GET /racing/runners/:runnerId/form",
  getJockeyStats: "GET /racing/jockeys/:jockeyId/stats",
  getTrainerStats: "GET /racing/trainers/:trainerId/stats",

  // Track information
  getTrackProfiles: "GET /racing/tracks",
  getTrackConditions: "GET /racing/tracks/:trackId/conditions",

  // Odds and betting
  getRaceOdds: "GET /racing/races/:raceId/odds",
  getBookmakerOdds: "GET /racing/odds/bookmaker/:bookmaker",
};
```

### 4. Content Management APIs

#### News and Articles

```javascript
const contentEndpoints = {
  // News articles
  getArticles: "GET /news",
  getArticle: "GET /news/:articleId",
  searchArticles: "GET /news/search",

  // Categories and tags
  getCategories: "GET /news/categories",
  getTags: "GET /news/tags",

  // User interactions
  saveArticle: "POST /news/:articleId/save",
  likeArticle: "POST /news/:articleId/like",
  commentOnArticle: "POST /news/:articleId/comments",

  // Expert tips
  getExpertTips: "GET /expert-tips",
  getTipDetails: "GET /expert-tips/:tipId",
};
```

## 🔧 Technical Implementation

### HTTP Client Configuration

```javascript
// Enhanced Axios configuration
class APIClient {
  constructor() {
    this.client = axios.create({
      baseURL: Config.baseURL,
      timeout: 15000,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    });

    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add authentication token
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp
        config.metadata = { startTime: new Date() };

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        // Calculate request duration
        const duration = new Date() - response.config.metadata.startTime;
        console.log(`API Request took ${duration}ms`);

        return response;
      },
      (error) => {
        this.handleAPIError(error);
        return Promise.reject(error);
      }
    );
  }

  handleAPIError(error) {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 401:
          this.handleUnauthorized();
          break;
        case 403:
          this.handleForbidden();
          break;
        case 429:
          this.handleRateLimit();
          break;
        case 500:
          this.handleServerError();
          break;
        default:
          console.error("API Error:", data);
      }
    } else if (error.request) {
      // Network error
      this.handleNetworkError();
    }
  }

  async get(url, config = {}) {
    return this.client.get(url, config);
  }

  async post(url, data = {}, config = {}) {
    return this.client.post(url, data, config);
  }

  async put(url, data = {}, config = {}) {
    return this.client.put(url, data, config);
  }

  async delete(url, config = {}) {
    return this.client.delete(url, config);
  }
}

export const apiClient = new APIClient();
```

### Error Handling Strategy

```javascript
const errorHandling = {
  networkErrors: {
    timeout: "retry_with_exponential_backoff",
    connectionLost: "queue_requests_for_retry",
    serverDown: "show_maintenance_message",
  },
  httpErrors: {
    400: "validation_error_display",
    401: "redirect_to_login",
    403: "show_permission_error",
    404: "show_not_found",
    429: "rate_limit_warning",
    500: "show_server_error",
  },
  dataErrors: {
    malformed: "use_fallback_data",
    missing: "show_placeholder",
    stale: "background_refresh",
  },
};
```

### Caching Strategy

```javascript
// API response caching
class APICache {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map();
  }

  set(key, data, ttlSeconds = 300) {
    this.cache.set(key, data);
    this.ttl.set(key, Date.now() + ttlSeconds * 1000);
  }

  get(key) {
    if (this.isExpired(key)) {
      this.delete(key);
      return null;
    }
    return this.cache.get(key);
  }

  isExpired(key) {
    const expiry = this.ttl.get(key);
    return expiry && Date.now() > expiry;
  }

  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  clear() {
    this.cache.clear();
    this.ttl.clear();
  }
}

const apiCache = new APICache();
```

## 🔄 Real-time Data Integration

### WebSocket Implementation

```javascript
// WebSocket connection management
class WebSocketManager {
  constructor() {
    this.connections = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect(namespace, options = {}) {
    const socket = io(`${Config.socketUrl}${namespace}`, {
      path: Config.socketPath,
      transports: ["websocket", "polling"],
      ...options,
    });

    socket.on("connect", () => {
      console.log(`Connected to ${namespace}`);
      this.reconnectAttempts = 0;
    });

    socket.on("disconnect", () => {
      console.log(`Disconnected from ${namespace}`);
      this.handleReconnection(namespace, options);
    });

    socket.on("error", (error) => {
      console.error(`WebSocket error on ${namespace}:`, error);
    });

    this.connections.set(namespace, socket);
    return socket;
  }

  handleReconnection(namespace, options) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000;

      setTimeout(() => {
        this.connect(namespace, options);
      }, delay);
    }
  }

  disconnect(namespace) {
    const socket = this.connections.get(namespace);
    if (socket) {
      socket.disconnect();
      this.connections.delete(namespace);
    }
  }

  disconnectAll() {
    this.connections.forEach((socket, namespace) => {
      this.disconnect(namespace);
    });
  }
}

export const wsManager = new WebSocketManager();
```

## 📊 Data Synchronization

### Sync Strategy

```javascript
const dataSyncStrategy = {
  immediate: {
    triggers: ["user_action", "critical_update"],
    method: "real_time_api_call",
    fallback: "cached_data",
  },
  periodic: {
    interval: "5_minutes",
    data: ["fixtures", "standings", "news"],
    method: "background_sync",
  },
  onDemand: {
    triggers: ["page_load", "user_request"],
    method: "lazy_loading",
    caching: "aggressive",
  },
};
```

### Offline Support

```javascript
// Service worker for offline functionality
const offlineStrategy = {
  cacheFirst: ["static_assets", "images", "styles"],
  networkFirst: ["api_calls", "live_data"],
  staleWhileRevalidate: ["news_articles", "team_info"],
  cacheOnly: ["offline_pages"],
  networkOnly: ["authentication", "payments"],
};
```

## 🔒 Security Implementation

### API Security

```javascript
const securityMeasures = {
  authentication: {
    method: "JWT_tokens",
    expiry: "24_hours",
    refresh: "automatic",
    storage: "httpOnly_cookies",
  },
  authorization: {
    method: "role_based_access",
    permissions: "granular",
    validation: "server_side",
  },
  dataProtection: {
    encryption: "TLS_1.3",
    validation: "input_sanitization",
    rateLimit: "per_user_per_endpoint",
  },
};
```

## 📈 Performance Optimization

### API Performance

```javascript
const performanceOptimizations = {
  requestOptimization: {
    batching: "multiple_requests_combined",
    compression: "gzip_enabled",
    caching: "intelligent_caching",
    pagination: "cursor_based",
  },
  responseOptimization: {
    minification: "json_minified",
    filtering: "field_selection",
    compression: "response_compression",
    streaming: "large_datasets",
  },
};
```

## 🚀 Future Enhancements

### Planned Improvements

- **GraphQL Integration**: More efficient data fetching
- **API Gateway**: Centralized API management
- **Microservices**: Service-oriented architecture
- **Event Sourcing**: Better data consistency
- **API Versioning**: Backward compatibility
