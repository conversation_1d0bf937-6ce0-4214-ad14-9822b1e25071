# 🎮 Fantasy Sports (SmartPlay) Documentation

SmartPlay is SmartB's daily fantasy sports platform that allows users to create fantasy teams and compete in various contests across multiple sports.

## 📋 Overview

SmartPlay provides:
- Daily fantasy sports contests
- Multi-sport coverage (Cricket, AFL, NRL, Basketball)
- Public and private contests
- Real-time scoring and leaderboards
- Prize pools and rewards
- Player statistics and analysis tools

## 🏗️ System Architecture

### Core Components

```
SmartPlay Platform/
├── Contest Management
│   ├── Contest Creation
│   ├── Entry Management
│   └── Prize Distribution
├── Team Building
│   ├── Player Selection
│   ├── Salary Cap Management
│   └── Lineup Optimization
├── Scoring Engine
│   ├── Real-time Scoring
│   ├── Statistical Updates
│   └── Leaderboard Management
└── User Interface
    ├── Contest Lobby
    ├── Team Builder
    └── Live Scoring
```

### Integration Points

#### SmartB Integration
```javascript
// Navigation to SmartPlay
const fantasyNavigation = {
  baseURL: Config.fantasySiteBaseURL,
  authToken: localStorage.getItem('auth_token'),
  userContext: {
    sport: 'cricket|afl|nrl|basketball',
    sportId: Number
  }
};
```

#### Cross-Platform Features
- **Single Sign-On**: Seamless login between SmartB and SmartPlay
- **User Profiles**: Shared user data and preferences
- **Payment Integration**: Unified payment and subscription system
- **Statistics Sharing**: Player data from sports system

## 🎯 Key Features

### 1. Contest Types

#### Daily Fantasy Sports (DFS)
```javascript
const contestTypes = {
  guaranteed: {
    description: 'Fixed prize pool regardless of entries',
    minEntries: 100,
    maxEntries: 10000,
    entryFee: 'Variable',
    prizePool: 'Guaranteed'
  },
  headToHead: {
    description: 'One-on-one competition',
    minEntries: 2,
    maxEntries: 2,
    entryFee: 'Equal',
    prizePool: 'Winner takes all (minus fees)'
  },
  multiplier: {
    description: 'Prize pool multiplies with entries',
    minEntries: 3,
    maxEntries: 'Unlimited',
    entryFee: 'Fixed',
    prizePool: 'Entry fee × multiplier'
  }
};
```

#### Sport-Specific Contests
- **Cricket Contests**: T20, ODI, Test match formats
- **AFL Contests**: Weekly round-based competitions
- **NRL Contests**: Round-by-round fantasy leagues
- **Basketball Contests**: NBA and international leagues

### 2. Team Building System

#### Player Selection Interface
```javascript
const teamBuilderModel = {
  sport: String,
  contest: String,
  salaryCap: Number,
  selectedPlayers: Array,
  remainingBudget: Number,
  positionRequirements: Object,
  playerPool: Array
};
```

#### Salary Cap Management
```javascript
const salaryCapRules = {
  cricket: {
    totalCap: 100,
    positions: {
      batsmen: { min: 3, max: 6 },
      bowlers: { min: 3, max: 6 },
      allRounders: { min: 1, max: 4 },
      wicketKeeper: { min: 1, max: 2 }
    }
  },
  afl: {
    totalCap: 100,
    positions: {
      forwards: { min: 6, max: 6 },
      midfielders: { min: 8, max: 8 },
      defenders: { min: 6, max: 6 },
      rucks: { min: 2, max: 2 }
    }
  }
};
```

### 3. Scoring System

#### Real-time Scoring Engine
```javascript
const scoringRules = {
  cricket: {
    batting: {
      run: 1,
      four: 1,
      six: 2,
      fifty: 8,
      century: 16,
      duck: -2
    },
    bowling: {
      wicket: 25,
      maiden: 12,
      fourWickets: 8,
      fiveWickets: 16
    },
    fielding: {
      catch: 8,
      stumping: 12,
      runOut: 12
    }
  },
  afl: {
    general: {
      kick: 3,
      handball: 2,
      mark: 3,
      tackle: 4,
      goal: 6,
      behind: 1
    },
    bonus: {
      disposalEfficiency: 'Variable',
      contested: 'Variable'
    }
  }
};
```

#### Live Updates
```javascript
// WebSocket connection for live scoring
const fantasySocket = io(`${Config.fantasySiteBaseURL}/live`);
fantasySocket.on('player_score_update', (data) => {
  updatePlayerScore(data.playerId, data.points);
  updateLeaderboard();
});
```

## 🔧 Technical Implementation

### Frontend Integration
```javascript
// SmartPlay navigation from SmartB
const handleFantasyNavigation = (sport, sportId) => {
  const fantasyURL = `${Config.fantasySiteBaseURL}?sports=${sport}&sport_id=${sportId}`;
  
  if (isUserAuthenticated()) {
    window.location.href = fantasyURL;
  } else {
    // Redirect to login with fantasy URL stored
    localStorage.setItem('redirect', JSON.stringify({ url: fantasyURL }));
    showLoginModal();
  }
};
```

### Authentication Flow
```javascript
const fantasyAuthFlow = {
  1: 'User clicks SmartPlay from SmartB',
  2: 'Check authentication status',
  3: 'If authenticated: Direct navigation with token',
  4: 'If not authenticated: Store redirect URL and show login',
  5: 'After login: Redirect to stored fantasy URL',
  6: 'SmartPlay validates token and creates session'
};
```

### Data Synchronization
```javascript
// Player data sync between platforms
const playerDataSync = {
  source: 'SmartB Sports System',
  destination: 'SmartPlay Player Pool',
  frequency: 'Real-time',
  data: {
    playerStats: 'Performance metrics',
    injuries: 'Injury status updates',
    teamChanges: 'Team selection changes',
    matchSchedule: 'Fixture updates'
  }
};
```

## 📊 Data Models

### Contest Model
```javascript
const contestModel = {
  id: String,
  name: String,
  sport: String,
  type: String, // 'guaranteed', 'headToHead', 'multiplier'
  entryFee: Number,
  prizePool: Number,
  maxEntries: Number,
  currentEntries: Number,
  startTime: Date,
  endTime: Date,
  status: String, // 'upcoming', 'live', 'completed'
  matches: Array,
  leaderboard: Array
};
```

### Fantasy Team Model
```javascript
const fantasyTeamModel = {
  id: String,
  userId: String,
  contestId: String,
  teamName: String,
  players: Array,
  totalSalary: Number,
  totalPoints: Number,
  rank: Number,
  isValid: Boolean,
  submissionTime: Date
};
```

### Player Model
```javascript
const fantasyPlayerModel = {
  id: String,
  name: String,
  team: String,
  position: String,
  salary: Number,
  projectedPoints: Number,
  actualPoints: Number,
  isPlaying: Boolean,
  injuryStatus: String,
  recentForm: Array
};
```

## 🎨 User Experience

### Contest Discovery
- **Featured Contests**: Highlighted high-value contests
- **Sport Filters**: Filter by preferred sports
- **Entry Fee Ranges**: Contests for all budget levels
- **Skill Levels**: Beginner to expert categories

### Team Building Interface
- **Drag & Drop**: Intuitive player selection
- **Auto-Fill**: Automatic lineup completion
- **Player Comparison**: Side-by-side player stats
- **Salary Tracker**: Real-time budget monitoring

### Live Experience
- **Real-time Scoring**: Live point updates
- **Leaderboard**: Dynamic ranking display
- **Player Performance**: Individual player tracking
- **Contest Chat**: Community interaction

## 💰 Monetization Model

### Revenue Streams
```javascript
const revenueModel = {
  entryFees: {
    percentage: '10-15%',
    description: 'Platform fee from contest entries'
  },
  subscriptions: {
    premium: 'Advanced statistics and tools',
    vip: 'Exclusive contests and benefits'
  },
  advertising: {
    sponsored: 'Sponsored contests and content',
    display: 'Banner and video advertisements'
  }
};
```

### Prize Distribution
```javascript
const prizeStructure = {
  guaranteed: {
    first: '20%',
    top10Percent: '60%',
    top20Percent: '20%'
  },
  headToHead: {
    winner: '180%', // 90% each entry minus 10% fee
    loser: '0%'
  }
};
```

## 🔒 Security & Compliance

### Fair Play Measures
- **Multi-entry Limits**: Prevent single-user dominance
- **Salary Cap Enforcement**: Strict budget validation
- **Late Swap Protection**: Prevent unfair advantages
- **Duplicate Detection**: Identify similar lineups

### Regulatory Compliance
- **Age Verification**: 18+ requirement enforcement
- **Geolocation**: Jurisdiction-based access control
- **Responsible Gaming**: Spending limits and self-exclusion
- **Data Protection**: GDPR and privacy compliance

## 📈 Analytics & Optimization

### User Metrics
- **Contest Participation**: Entry rates and preferences
- **Team Building Patterns**: Popular player combinations
- **Retention Rates**: User engagement over time
- **Revenue per User**: Monetization effectiveness

### Performance Monitoring
- **System Load**: Contest and user capacity
- **Scoring Accuracy**: Real-time update reliability
- **User Experience**: Interface responsiveness
- **Payment Processing**: Transaction success rates

## 🚀 Future Enhancements

### Planned Features
- **Social Features**: Friend leagues and challenges
- **Advanced Analytics**: AI-powered player recommendations
- **Live Streaming**: Integrated match viewing
- **Mobile App**: Dedicated fantasy sports app
- **Cryptocurrency**: Alternative payment methods

### Technical Improvements
- **Microservices**: Scalable architecture
- **Machine Learning**: Predictive modeling
- **Blockchain**: Transparent prize distribution
- **AR/VR**: Immersive fantasy experience

## 🐛 Common Issues & Solutions

### Technical Challenges
- **Scoring Delays**: Implement redundant data sources
- **High Traffic**: Auto-scaling infrastructure
- **Payment Issues**: Multiple payment gateways
- **Data Accuracy**: Real-time validation systems

### User Experience Issues
- **Complex Interface**: Simplified onboarding
- **Slow Loading**: Performance optimization
- **Mobile Compatibility**: Responsive design
- **Contest Confusion**: Clear rules and tutorials

## 📚 Related Documentation

- [Sports System Integration](./sports-system.md)
- [Payment Processing](./payment-system.md)
- [Real-time Data Handling](./websocket-integration.md)
- [Mobile App Development](./mobile-app.md)
