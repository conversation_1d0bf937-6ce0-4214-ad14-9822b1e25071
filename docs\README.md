# SmartB WebApp Documentation

This directory contains comprehensive documentation for all functionalities and features of the SmartB WebApp.

## 📚 Documentation Structure

### Core Features

- [🏇 Racing System](./racing-system.md) - Horse racing, greyhound racing, and harness racing
- [⚽ Sports System](./sports-system.md) - Multi-sport coverage and live data
- [🏆 Tipping Competitions](./tipping-competitions.md) - Create and manage tipping competitions
- 📊 Odds Comparison - Smart Odds Comparison tool
- [📰 News & Analysis (SmartInfo)](./news-system.md) - News and expert analysis

### User Management

- [👤 Authentication System](./authentication.md) - User login, registration, and security

### Technical Documentation

- [🏗️ Architecture Overview](./architecture.md) - System architecture and design patterns
- [🔌 API Integration](./api-integration.md) - Backend API endpoints and data flow

## 🎯 Quick Navigation

### For Developers

- Start with [Architecture Overview](./architecture.md)
- Check [API Integration](./api-integration.md)

### For Product Managers

- Review [Racing System](./racing-system.md)
- Understand [Tipping Competitions](./tipping-competitions.md)

## 📊 Feature Matrix

| Feature              | AU Region | IN Region | Mobile App | Web App |
| -------------------- | --------- | --------- | ---------- | ------- |
| Horse Racing         | ✅        | ❌        | ✅         | ✅      |
| Greyhound Racing     | ✅        | ❌        | ✅         | ✅      |
| Harness Racing       | ✅        | ❌        | ✅         | ✅      |
| Cricket              | ✅        | ✅        | ✅         | ✅      |
| AFL                  | ✅        | ❌        | ✅         | ✅      |
| NRL                  | ✅        | ❌        | ✅         | ✅      |
| Basketball           | ✅        | ✅        | ✅         | ✅      |
| Soccer               | ✅        | ✅        | ✅         | ✅      |
| Fantasy Sports       | ✅        | ✅        | ✅         | ✅      |
| Tipping Competitions | ✅        | ✅        | ✅         | ✅      |
| Odds Comparison      | ✅        | ❌        | ✅         | ✅      |
| News & Analysis      | ✅        | ✅        | ✅         | ✅      |
| Subscription System  | ✅        | ✅        | ✅         | ✅      |

## 🔄 Update Schedule

This documentation is updated with each major release. Last updated: Version 1.0.0

## 📞 Support

For documentation questions or updates, contact the development team.
